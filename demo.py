#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GitHub Release监控系统 - 快速演示脚本
"""

import os
import webbrowser
import time
import subprocess
import sys

def print_banner():
    """打印欢迎横幅"""
    print("=" * 60)
    print("🚀 GitHub Release监控系统")
    print("=" * 60)
    print("一个轻量级的GitHub仓库Release监控平台")
    print("实时追踪开源项目的发布动态")
    print("=" * 60)
    print()

def check_dependencies():
    """检查依赖"""
    print("📋 检查运行环境...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version.major >= 3 and python_version.minor >= 8:
        print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    else:
        print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}.{python_version.micro}")
        print("   需要Python 3.8+")
        return False
    
    # 检查必要文件
    required_files = ['simple_app.py', 'app.py', 'templates/index.html']
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ 找到文件: {file}")
        else:
            print(f"❌ 缺少文件: {file}")
            return False
    
    return True

def show_menu():
    """显示菜单"""
    print("\n📋 选择运行模式:")
    print("1. 🚀 简化版本 (推荐) - 无需安装依赖，立即体验")
    print("2. 🔧 完整版本 - 需要安装Flask等依赖")
    print("3. 📖 查看项目文档")
    print("4. 🌐 打开GitHub仓库 (如果有)")
    print("5. ❌ 退出")
    print()

def run_simple_version():
    """运行简化版本"""
    print("🚀 启动简化版本...")
    print("📱 应用将在 http://localhost:8000 启动")
    print("⏰ 请等待3秒后自动打开浏览器...")
    print()
    
    try:
        # 启动应用
        process = subprocess.Popen([sys.executable, 'simple_app.py'])
        
        # 等待3秒让服务器启动
        time.sleep(3)
        
        # 自动打开浏览器
        webbrowser.open('http://localhost:8000')
        
        print("🎉 应用已启动！")
        print("📋 功能说明:")
        print("   - 查看监控仓库列表")
        print("   - 添加新的GitHub仓库")
        print("   - 查看Release历史记录")
        print("   - RESTful API接口")
        print()
        print("⚠️  注意: 这是演示版本，包含示例数据")
        print("按 Ctrl+C 停止服务器")
        
        # 等待用户中断
        process.wait()
        
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
        process.terminate()
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def run_full_version():
    """运行完整版本"""
    print("🔧 准备启动完整版本...")
    
    # 检查是否已安装依赖
    try:
        import flask
        print("✅ Flask已安装")
        
        print("🚀 启动完整版本...")
        print("📱 应用将在 http://localhost:5000 启动")
        
        # 启动应用
        process = subprocess.Popen([sys.executable, 'app.py'])
        
        # 等待3秒让服务器启动
        time.sleep(3)
        
        # 自动打开浏览器
        webbrowser.open('http://localhost:5000')
        
        print("🎉 完整版应用已启动！")
        print("📋 完整功能:")
        print("   - GitHub API集成")
        print("   - 自动数据同步")
        print("   - 定时任务调度")
        print("   - 生产环境就绪")
        print()
        print("按 Ctrl+C 停止服务器")
        
        # 等待用户中断
        process.wait()
        
    except ImportError:
        print("❌ 未安装Flask等依赖")
        print("📦 请先运行安装脚本:")
        print("   ./install.sh")
        print()
        print("或手动安装依赖:")
        print("   pip install flask sqlalchemy pygithub apscheduler requests python-dotenv")
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
        process.terminate()
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def show_documentation():
    """显示文档"""
    print("📖 项目文档")
    print("=" * 40)
    
    docs = [
        ("README.md", "项目主要文档"),
        ("PROJECT_SUMMARY.md", "项目总结"),
        (".env.example", "环境配置示例"),
        ("install.sh", "自动安装脚本")
    ]
    
    for doc, desc in docs:
        if os.path.exists(doc):
            print(f"✅ {doc} - {desc}")
        else:
            print(f"❌ {doc} - {desc} (文件不存在)")
    
    print()
    print("📋 快速开始:")
    print("1. 运行简化版本: python3 simple_app.py")
    print("2. 安装完整版本: ./install.sh")
    print("3. 启动完整版本: python3 app.py")
    print()

def open_github():
    """打开GitHub仓库"""
    print("🌐 GitHub仓库功能")
    print("如果这是一个GitHub项目，可以在这里添加仓库链接")
    print("目前这是本地项目，没有远程仓库")

def main():
    """主函数"""
    print_banner()
    
    if not check_dependencies():
        print("❌ 环境检查失败，请检查Python版本和项目文件")
        return
    
    while True:
        show_menu()
        
        try:
            choice = input("请选择 (1-5): ").strip()
            
            if choice == '1':
                run_simple_version()
            elif choice == '2':
                run_full_version()
            elif choice == '3':
                show_documentation()
            elif choice == '4':
                open_github()
            elif choice == '5':
                print("👋 再见！")
                break
            else:
                print("❌ 无效选择，请输入1-5")
                
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")
        
        print("\n" + "=" * 60)

if __name__ == '__main__':
    main()
