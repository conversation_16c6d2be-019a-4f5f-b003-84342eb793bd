#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GitHub Release监控系统 - 简化版本（用于演示）
不依赖外部库，使用Python标准库实现基本功能
"""

import json
import sqlite3
import os
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading
import time

# 数据库初始化
def init_database():
    """初始化SQLite数据库"""
    conn = sqlite3.connect('github_monitor.db')
    cursor = conn.cursor()
    
    # 创建repositories表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS repositories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            full_name TEXT NOT NULL,
            description TEXT,
            stars INTEGER DEFAULT 0,
            forks INTEGER DEFAULT 0,
            issues INTEGER DEFAULT 0,
            language TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 创建releases表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS releases (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            repo_id INTEGER NOT NULL,
            tag_name TEXT NOT NULL,
            name TEXT,
            body TEXT,
            published_at TIMESTAMP,
            download_url TEXT,
            is_prerelease INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (repo_id) REFERENCES repositories (id)
        )
    ''')
    
    conn.commit()
    conn.close()
    print("✅ 数据库初始化完成")

# 数据库操作类
class DatabaseManager:
    @staticmethod
    def get_connection():
        return sqlite3.connect('github_monitor.db')
    
    @staticmethod
    def add_sample_data():
        """添加示例数据"""
        conn = DatabaseManager.get_connection()
        cursor = conn.cursor()
        
        # 添加示例仓库
        sample_repos = [
            ('microsoft/vscode', 'Microsoft Visual Studio Code', 'Visual Studio Code', 150000, 25000, 5000, 'TypeScript'),
            ('facebook/react', 'Facebook React', 'A declarative, efficient, and flexible JavaScript library', 200000, 40000, 800, 'JavaScript'),
            ('vuejs/vue', 'Vue.js', 'The Progressive JavaScript Framework', 180000, 28000, 600, 'JavaScript'),
        ]
        
        for repo_data in sample_repos:
            cursor.execute('''
                INSERT OR IGNORE INTO repositories 
                (name, full_name, description, stars, forks, issues, language)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', repo_data)
        
        # 添加示例Release
        cursor.execute('SELECT id FROM repositories WHERE name = ?', ('microsoft/vscode',))
        repo_id = cursor.fetchone()
        if repo_id:
            sample_releases = [
                (repo_id[0], 'v1.85.0', 'December 2023 Release', 'New features and bug fixes', '2023-12-01 10:00:00', 'https://github.com/microsoft/vscode/releases/tag/v1.85.0', 0),
                (repo_id[0], 'v1.84.2', 'November 2023 Patch', 'Bug fixes and improvements', '2023-11-15 14:30:00', 'https://github.com/microsoft/vscode/releases/tag/v1.84.2', 0),
            ]
            
            for release_data in sample_releases:
                cursor.execute('''
                    INSERT OR IGNORE INTO releases 
                    (repo_id, tag_name, name, body, published_at, download_url, is_prerelease)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', release_data)
        
        conn.commit()
        conn.close()
        print("✅ 示例数据添加完成")
    
    @staticmethod
    def get_all_repositories():
        """获取所有仓库"""
        conn = DatabaseManager.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM repositories ORDER BY stars DESC')
        repos = cursor.fetchall()
        conn.close()
        return repos
    
    @staticmethod
    def get_repository_releases(repo_id, limit=20):
        """获取仓库的Release记录"""
        conn = DatabaseManager.get_connection()
        cursor = conn.cursor()
        cursor.execute('''
            SELECT * FROM releases 
            WHERE repo_id = ? 
            ORDER BY published_at DESC 
            LIMIT ?
        ''', (repo_id, limit))
        releases = cursor.fetchall()
        conn.close()
        return releases

# HTTP请求处理器
class GitHubMonitorHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        """处理GET请求"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        if path == '/' or path == '/index.html':
            self.serve_index()
        elif path == '/add_repo':
            self.serve_add_repo()
        elif path.startswith('/repo/'):
            repo_id = path.split('/')[-1]
            self.serve_repo_detail(repo_id)
        elif path == '/api/repos':
            self.serve_api_repos()
        elif path.startswith('/static/'):
            self.serve_static_file(path)
        else:
            self.send_error(404)
    
    def do_POST(self):
        """处理POST请求"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        if path == '/add_repo':
            self.handle_add_repo()
        else:
            self.send_error(404)
    
    def serve_index(self):
        """首页"""
        repos = DatabaseManager.get_all_repositories()
        
        html = f'''
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>GitHub Release监控系统</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        </head>
        <body>
            <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
                <div class="container">
                    <a class="navbar-brand" href="/">
                        <i class="fab fa-github me-2"></i>GitHub Release监控
                    </a>
                    <div class="navbar-nav ms-auto">
                        <a class="nav-link" href="/add_repo">
                            <i class="fas fa-plus me-1"></i>添加仓库
                        </a>
                    </div>
                </div>
            </nav>
            
            <div class="container mt-4">
                <h1 class="h3 mb-4">
                    <i class="fas fa-repository me-2"></i>监控仓库列表
                    <span class="badge bg-primary ms-2">{len(repos)}</span>
                </h1>
                
                <div class="row">
        '''
        
        for repo in repos:
            html += f'''
                    <div class="col-lg-6 col-xl-4 mb-4">
                        <div class="card h-100 shadow-sm">
                            <div class="card-header bg-light">
                                <h5 class="card-title mb-0">
                                    <a href="/repo/{repo[0]}" class="text-decoration-none">
                                        {repo[1]}
                                    </a>
                                </h5>
                            </div>
                            <div class="card-body">
                                <p class="card-text text-muted small mb-3">{repo[3] or '暂无描述'}</p>
                                <div class="row text-center">
                                    <div class="col-4">
                                        <i class="fas fa-star text-warning"></i>
                                        <div class="fw-bold">{repo[4]}</div>
                                        <small class="text-muted">Stars</small>
                                    </div>
                                    <div class="col-4">
                                        <i class="fas fa-code-branch text-info"></i>
                                        <div class="fw-bold">{repo[5]}</div>
                                        <small class="text-muted">Forks</small>
                                    </div>
                                    <div class="col-4">
                                        <i class="fas fa-exclamation-circle text-danger"></i>
                                        <div class="fw-bold">{repo[6]}</div>
                                        <small class="text-muted">Issues</small>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer bg-light">
                                <small class="text-muted">
                                    <i class="fas fa-code me-1"></i>{repo[7] or 'Unknown'}
                                </small>
                            </div>
                        </div>
                    </div>
            '''
        
        if not repos:
            html += '''
                    <div class="col-12">
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">暂无监控仓库</h4>
                            <p class="text-muted">点击上方"添加仓库"按钮开始监控GitHub仓库</p>
                            <a href="/add_repo" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>添加第一个仓库
                            </a>
                        </div>
                    </div>
            '''
        
        html += '''
                </div>
            </div>
            
            <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
        </body>
        </html>
        '''
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def serve_add_repo(self):
        """添加仓库页面"""
        html = '''
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>添加仓库 - GitHub Release监控系统</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        </head>
        <body>
            <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
                <div class="container">
                    <a class="navbar-brand" href="/">
                        <i class="fab fa-github me-2"></i>GitHub Release监控
                    </a>
                </div>
            </nav>
            
            <div class="container mt-4">
                <div class="row justify-content-center">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h4 class="card-title mb-0">
                                    <i class="fas fa-plus me-2"></i>添加GitHub仓库
                                </h4>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="/add_repo">
                                    <div class="mb-3">
                                        <label for="repo_name" class="form-label">
                                            <i class="fab fa-github me-1"></i>仓库名称
                                        </label>
                                        <input type="text" class="form-control" id="repo_name" name="repo_name" 
                                               placeholder="例如: microsoft/vscode" required>
                                        <div class="form-text">
                                            请输入完整的仓库名称，格式为: 用户名/仓库名
                                        </div>
                                    </div>
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-plus me-1"></i>添加仓库
                                        </button>
                                        <a href="/" class="btn btn-outline-secondary">
                                            <i class="fas fa-arrow-left me-1"></i>返回首页
                                        </a>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
        </body>
        </html>
        '''
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def serve_repo_detail(self, repo_id):
        """仓库详情页面"""
        try:
            repo_id = int(repo_id)
            conn = DatabaseManager.get_connection()
            cursor = conn.cursor()
            
            # 获取仓库信息
            cursor.execute('SELECT * FROM repositories WHERE id = ?', (repo_id,))
            repo = cursor.fetchone()
            
            if not repo:
                self.send_error(404)
                return
            
            # 获取Release记录
            releases = DatabaseManager.get_repository_releases(repo_id)
            
            html = f'''
            <!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>{repo[1]} - GitHub Release监控系统</title>
                <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
            </head>
            <body>
                <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
                    <div class="container">
                        <a class="navbar-brand" href="/">
                            <i class="fab fa-github me-2"></i>GitHub Release监控
                        </a>
                    </div>
                </nav>
                
                <div class="container mt-4">
                    <div class="card mb-4">
                        <div class="card-body">
                            <h1 class="h3 mb-2">
                                <i class="fab fa-github me-2"></i>{repo[1]}
                            </h1>
                            <p class="text-muted mb-3">{repo[3] or '暂无描述'}</p>
                            <div class="row">
                                <div class="col-auto">
                                    <span class="badge bg-warning text-dark">
                                        <i class="fas fa-star me-1"></i>{repo[4]} Stars
                                    </span>
                                </div>
                                <div class="col-auto">
                                    <span class="badge bg-info">
                                        <i class="fas fa-code-branch me-1"></i>{repo[5]} Forks
                                    </span>
                                </div>
                                <div class="col-auto">
                                    <span class="badge bg-danger">
                                        <i class="fas fa-exclamation-circle me-1"></i>{repo[6]} Issues
                                    </span>
                                </div>
                                <div class="col-auto">
                                    <span class="badge bg-secondary">
                                        <i class="fas fa-code me-1"></i>{repo[7] or 'Unknown'}
                                    </span>
                                </div>
                            </div>
                            <div class="mt-3">
                                <a href="/" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>返回首页
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title mb-0">
                                <i class="fas fa-tags me-2"></i>Release历史记录
                                <span class="badge bg-primary ms-2">{len(releases)}</span>
                            </h4>
                        </div>
                        <div class="card-body">
            '''
            
            if releases:
                for release in releases:
                    html += f'''
                            <div class="card mb-3">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <span class="badge bg-dark me-2">{release[2]}</span>
                                        {release[3] or release[2]}
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <p>{release[4] or '暂无说明'}</p>
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        发布时间: {release[5] or '未知'}
                                    </small>
                                </div>
                                {f'<div class="card-footer"><a href="{release[6]}" target="_blank" class="btn btn-sm btn-outline-primary"><i class="fas fa-download me-1"></i>下载</a></div>' if release[6] else ''}
                            </div>
                    '''
            else:
                html += '''
                            <div class="text-center py-5">
                                <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">暂无Release记录</h5>
                                <p class="text-muted">该仓库还没有发布任何Release版本</p>
                            </div>
                '''
            
            html += '''
                        </div>
                    </div>
                </div>
                
                <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
            </body>
            </html>
            '''
            
            conn.close()
            
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            self.wfile.write(html.encode('utf-8'))
            
        except ValueError:
            self.send_error(400)
    
    def handle_add_repo(self):
        """处理添加仓库请求"""
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length).decode('utf-8')
        
        # 简单解析表单数据
        repo_name = ''
        for param in post_data.split('&'):
            if param.startswith('repo_name='):
                repo_name = param.split('=')[1].replace('+', ' ')
                break
        
        if repo_name and '/' in repo_name:
            # 这里应该调用GitHub API获取真实数据，现在只是添加示例数据
            conn = DatabaseManager.get_connection()
            cursor = conn.cursor()
            
            try:
                cursor.execute('''
                    INSERT INTO repositories (name, full_name, description, stars, forks, issues, language)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (repo_name, repo_name, f'{repo_name} 仓库', 1000, 200, 50, 'Python'))
                conn.commit()
                success = True
            except sqlite3.IntegrityError:
                success = False
            
            conn.close()
        else:
            success = False
        
        # 重定向到首页
        self.send_response(302)
        self.send_header('Location', '/')
        self.end_headers()
    
    def serve_api_repos(self):
        """API: 获取所有仓库"""
        repos = DatabaseManager.get_all_repositories()
        data = []
        for repo in repos:
            data.append({
                'id': repo[0],
                'name': repo[1],
                'full_name': repo[2],
                'description': repo[3],
                'stars': repo[4],
                'forks': repo[5],
                'issues': repo[6],
                'language': repo[7]
            })
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(data, ensure_ascii=False).encode('utf-8'))
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")

def main():
    """主函数"""
    print("🚀 启动GitHub Release监控系统 (简化版)")
    
    # 初始化数据库
    init_database()
    
    # 添加示例数据
    DatabaseManager.add_sample_data()
    
    # 启动HTTP服务器
    server_address = ('', 8000)
    httpd = HTTPServer(server_address, GitHubMonitorHandler)
    
    print(f"🌐 服务器启动成功!")
    print(f"📱 访问地址: http://localhost:8000")
    print(f"📝 功能说明:")
    print(f"   - 查看监控仓库列表")
    print(f"   - 添加新的GitHub仓库")
    print(f"   - 查看Release历史记录")
    print(f"   - RESTful API接口")
    print(f"")
    print(f"⚠️  注意: 这是简化版本，用于演示基本功能")
    print(f"   完整版本需要安装Flask等依赖库")
    print(f"")
    print(f"按 Ctrl+C 停止服务器")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
        httpd.server_close()

if __name__ == '__main__':
    main()
