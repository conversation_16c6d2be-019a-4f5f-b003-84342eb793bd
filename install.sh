#!/bin/bash
# GitHub Release监控系统 - 安装脚本

set -e

echo "🚀 GitHub Release监控系统安装脚本"
echo "=================================="

# 检查Python版本
echo "📋 检查Python环境..."
if command -v python3 &> /dev/null; then
    PYTHON_VERSION=$(python3 --version 2>&1 | awk '{print $2}')
    echo "✅ Python版本: $PYTHON_VERSION"
else
    echo "❌ 未找到Python3，请先安装Python 3.8+"
    exit 1
fi

# 检查是否有uv
echo "📋 检查包管理器..."
if command -v uv &> /dev/null; then
    echo "✅ 找到uv包管理器"
    USE_UV=true
elif command -v pip3 &> /dev/null; then
    echo "✅ 找到pip3包管理器"
    USE_UV=false
else
    echo "❌ 未找到包管理器，请先安装pip或uv"
    exit 1
fi

# 创建虚拟环境
echo "📦 创建虚拟环境..."
if [ "$USE_UV" = true ]; then
    if [ ! -d ".venv" ]; then
        uv venv
        echo "✅ uv虚拟环境创建完成"
    else
        echo "✅ 虚拟环境已存在"
    fi
else
    if [ ! -d "venv" ]; then
        python3 -m venv venv
        echo "✅ Python虚拟环境创建完成"
    else
        echo "✅ 虚拟环境已存在"
    fi
fi

# 激活虚拟环境并安装依赖
echo "📚 安装依赖包..."
if [ "$USE_UV" = true ]; then
    echo "使用uv安装依赖..."
    uv sync || {
        echo "⚠️  uv sync失败，尝试使用pip安装..."
        source .venv/bin/activate
        pip install flask sqlalchemy pygithub apscheduler requests python-dotenv
    }
else
    echo "使用pip安装依赖..."
    source venv/bin/activate
    pip install flask sqlalchemy pygithub apscheduler requests python-dotenv
fi

# 创建环境配置文件
echo "⚙️  创建配置文件..."
if [ ! -f ".env" ]; then
    cp .env.example .env
    echo "✅ 已创建.env配置文件"
    echo "📝 请编辑.env文件，配置GitHub Token等参数"
else
    echo "✅ .env配置文件已存在"
fi

# 创建启动脚本
echo "📝 创建启动脚本..."
cat > start.sh << 'EOF'
#!/bin/bash
# GitHub Release监控系统启动脚本

echo "🚀 启动GitHub Release监控系统..."

# 激活虚拟环境
if [ -d ".venv" ]; then
    source .venv/bin/activate
elif [ -d "venv" ]; then
    source venv/bin/activate
else
    echo "❌ 未找到虚拟环境，请先运行install.sh"
    exit 1
fi

# 启动应用
python app.py
EOF

chmod +x start.sh

echo ""
echo "🎉 安装完成！"
echo "==============="
echo ""
echo "📋 下一步操作："
echo "1. 编辑 .env 文件，配置GitHub Token (可选但推荐)"
echo "2. 运行 ./start.sh 启动应用"
echo "3. 或者运行 python3 simple_app.py 启动简化版本"
echo ""
echo "🌐 访问地址："
echo "- 完整版本: http://localhost:5000"
echo "- 简化版本: http://localhost:8000"
echo ""
echo "📖 更多信息请查看 README.md"
echo ""
echo "⚠️  提示："
echo "- 简化版本不需要安装依赖，可直接运行"
echo "- 完整版本需要安装Flask等依赖库"
echo "- 建议配置GitHub Token以获得更高的API调用限制"
