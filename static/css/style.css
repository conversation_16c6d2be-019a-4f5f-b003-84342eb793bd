/* GitHub Release监控系统 - 自定义样式 */

/* 全局样式 */
body {
    background-color: #f8f9fa;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: 600;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 8px;
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

/* 统计数据样式 */
.stat-item {
    text-align: center;
    padding: 10px 0;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 600;
    color: #495057;
    margin: 5px 0;
}

.stat-label {
    font-size: 0.8rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 徽章样式 */
.badge {
    font-size: 0.75rem;
    padding: 0.5em 0.75em;
}

/* 按钮样式 */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
}

/* 表单样式 */
.form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 警告框样式 */
.alert {
    border: none;
    border-radius: 8px;
    border-left: 4px solid;
}

.alert-success {
    border-left-color: #28a745;
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    border-left-color: #dc3545;
    background-color: #f8d7da;
    color: #721c24;
}

.alert-info {
    border-left-color: #17a2b8;
    background-color: #d1ecf1;
    color: #0c5460;
}

/* 时间线样式 */
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #007bff, #6c757d);
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 10px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: white;
    border: 3px solid #007bff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    z-index: 1;
}

.timeline-content {
    margin-left: 20px;
}

/* Release说明样式 */
.release-notes {
    max-height: 300px;
    overflow-y: auto;
    font-size: 14px;
    line-height: 1.6;
    color: #495057;
}

.release-notes h1,
.release-notes h2,
.release-notes h3,
.release-notes h4,
.release-notes h5,
.release-notes h6 {
    margin-top: 1rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.release-notes ul,
.release-notes ol {
    padding-left: 1.5rem;
}

.release-notes code {
    background-color: #f8f9fa;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-size: 0.875em;
    color: #e83e8c;
}

.release-notes pre {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 6px;
    font-size: 0.875em;
    overflow-x: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .stat-number {
        font-size: 1.2rem;
    }
    
    .timeline {
        padding-left: 20px;
    }
    
    .timeline-marker {
        left: -15px;
        width: 24px;
        height: 24px;
        font-size: 10px;
    }
    
    .timeline-content {
        margin-left: 15px;
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
}

.empty-state i {
    font-size: 4rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.empty-state h4 {
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.empty-state p {
    color: #6c757d;
    margin-bottom: 1.5rem;
}

/* 页脚样式 */
footer {
    margin-top: auto;
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

/* 下拉菜单样式 */
.dropdown-menu {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border-radius: 8px;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    transition: background-color 0.15s ease-in-out;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

/* 自定义滚动条 */
.release-notes::-webkit-scrollbar {
    width: 6px;
}

.release-notes::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.release-notes::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.release-notes::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
