// GitHub Release监控系统 - 主要JavaScript功能

document.addEventListener('DOMContentLoaded', function() {
    // 初始化所有功能
    initTooltips();
    initConfirmDialogs();
    initAutoRefresh();
    initFormValidation();
    initLoadingStates();
});

// 初始化工具提示
function initTooltips() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// 初始化确认对话框
function initConfirmDialogs() {
    // 删除确认
    document.querySelectorAll('form[onsubmit*="confirm"]').forEach(function(form) {
        form.addEventListener('submit', function(e) {
            const message = form.getAttribute('onsubmit').match(/confirm\('([^']+)'\)/);
            if (message && !confirm(message[1])) {
                e.preventDefault();
            }
        });
    });
}

// 初始化自动刷新功能
function initAutoRefresh() {
    // 仅在首页启用自动刷新
    if (window.location.pathname === '/') {
        // 每5分钟自动刷新一次
        setTimeout(function() {
            if (document.visibilityState === 'visible') {
                location.reload();
            }
        }, 300000); // 5分钟
    }
}

// 初始化表单验证
function initFormValidation() {
    // 仓库名称验证
    const repoInput = document.getElementById('repo_name');
    if (repoInput) {
        repoInput.addEventListener('input', function() {
            validateRepoName(this);
        });
        
        repoInput.addEventListener('blur', function() {
            validateRepoName(this);
        });
    }
}

// 验证仓库名称格式
function validateRepoName(input) {
    const value = input.value.trim();
    const pattern = /^[a-zA-Z0-9._-]+\/[a-zA-Z0-9._-]+$/;
    
    if (value && !pattern.test(value)) {
        input.classList.add('is-invalid');
        showInputError(input, '请输入正确的仓库名称格式: 用户名/仓库名');
    } else {
        input.classList.remove('is-invalid');
        hideInputError(input);
    }
}

// 显示输入错误
function showInputError(input, message) {
    let errorDiv = input.parentNode.querySelector('.invalid-feedback');
    if (!errorDiv) {
        errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        input.parentNode.appendChild(errorDiv);
    }
    errorDiv.textContent = message;
}

// 隐藏输入错误
function hideInputError(input) {
    const errorDiv = input.parentNode.querySelector('.invalid-feedback');
    if (errorDiv) {
        errorDiv.remove();
    }
}

// 初始化加载状态
function initLoadingStates() {
    // 为所有提交按钮添加加载状态
    document.querySelectorAll('form').forEach(function(form) {
        form.addEventListener('submit', function() {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                showLoadingState(submitBtn);
            }
        });
    });
}

// 显示加载状态
function showLoadingState(button) {
    const originalText = button.innerHTML;
    button.disabled = true;
    button.innerHTML = '<span class="loading me-2"></span>处理中...';
    
    // 保存原始文本，以便恢复
    button.dataset.originalText = originalText;
}

// 隐藏加载状态
function hideLoadingState(button) {
    button.disabled = false;
    button.innerHTML = button.dataset.originalText || button.innerHTML;
}

// 显示成功消息
function showSuccessMessage(message) {
    showAlert(message, 'success');
}

// 显示错误消息
function showErrorMessage(message) {
    showAlert(message, 'danger');
}

// 显示警告消息
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // 插入到页面顶部
    const container = document.querySelector('main .container');
    if (container) {
        container.insertBefore(alertDiv, container.firstChild);
        
        // 3秒后自动消失
        setTimeout(function() {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 3000);
    }
}

// 格式化时间
function formatTime(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now - date;
    
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);
    
    if (minutes < 1) {
        return '刚刚';
    } else if (minutes < 60) {
        return `${minutes}分钟前`;
    } else if (hours < 24) {
        return `${hours}小时前`;
    } else if (days < 7) {
        return `${days}天前`;
    } else {
        return date.toLocaleDateString('zh-CN');
    }
}

// 格式化数字
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

// 复制到剪贴板
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(function() {
            showSuccessMessage('已复制到剪贴板');
        }).catch(function() {
            fallbackCopyToClipboard(text);
        });
    } else {
        fallbackCopyToClipboard(text);
    }
}

// 备用复制方法
function fallbackCopyToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        document.execCommand('copy');
        showSuccessMessage('已复制到剪贴板');
    } catch (err) {
        showErrorMessage('复制失败，请手动复制');
    }
    
    document.body.removeChild(textArea);
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 检查网络状态
function checkNetworkStatus() {
    if (!navigator.onLine) {
        showErrorMessage('网络连接已断开，请检查网络设置');
    }
}

// 监听网络状态变化
window.addEventListener('online', function() {
    showSuccessMessage('网络连接已恢复');
});

window.addEventListener('offline', function() {
    showErrorMessage('网络连接已断开');
});

// 页面可见性变化处理
document.addEventListener('visibilitychange', function() {
    if (document.visibilityState === 'visible') {
        // 页面变为可见时，检查是否需要刷新数据
        const lastUpdate = localStorage.getItem('lastUpdate');
        const now = Date.now();
        
        if (lastUpdate && (now - parseInt(lastUpdate)) > 300000) { // 5分钟
            // 可以在这里添加数据刷新逻辑
            console.log('页面重新可见，可能需要刷新数据');
        }
    }
});

// 保存最后更新时间
function updateLastUpdateTime() {
    localStorage.setItem('lastUpdate', Date.now().toString());
}

// 页面加载完成后更新时间
window.addEventListener('load', updateLastUpdateTime);
