# GitHub Release监控系统 - 环境配置示例
# 复制此文件为 .env 并填入实际配置值

# Flask应用配置
SECRET_KEY=your-secret-key-here
FLASK_ENV=development
FLASK_DEBUG=True

# 数据库配置
DATABASE_URL=sqlite:///github_monitor.db

# GitHub API配置
# 获取GitHub Personal Access Token: https://github.com/settings/tokens
# 需要的权限: public_repo (用于访问公开仓库信息)
GITHUB_TOKEN=your-github-token-here

# 应用配置
APP_HOST=0.0.0.0
APP_PORT=5000

# 同步配置
SYNC_INTERVAL_HOURS=1
MAX_RELEASES_PER_REPO=20
MAX_MONITORED_REPOS=200

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=app.log
