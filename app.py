#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GitHub Release监控系统主应用
"""

import os
import logging
from datetime import datetime
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Text, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from github import Github
from apscheduler.schedulers.background import BackgroundScheduler
import requests
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Flask应用初始化
app = Flask(__name__)
app.secret_key = os.getenv('SECRET_KEY', 'your-secret-key-here')

# 数据库配置
DATABASE_URL = os.getenv('DATABASE_URL', 'sqlite:///github_monitor.db')
engine = create_engine(DATABASE_URL, echo=False)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# GitHub API配置
GITHUB_TOKEN = os.getenv('GITHUB_TOKEN')
github_client = Github(GITHUB_TOKEN) if GITHUB_TOKEN else Github()

# 数据库模型
class Repository(Base):
    """仓库信息表"""
    __tablename__ = 'repositories'
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False, unique=True)  # 格式: owner/repo
    full_name = Column(String(255), nullable=False)
    description = Column(Text)
    stars = Column(Integer, default=0)
    forks = Column(Integer, default=0)
    issues = Column(Integer, default=0)
    language = Column(String(50))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联关系
    releases = relationship("Release", back_populates="repository", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Repository(name='{self.name}', stars={self.stars})>"


class Release(Base):
    """发布记录表"""
    __tablename__ = 'releases'
    
    id = Column(Integer, primary_key=True, index=True)
    repo_id = Column(Integer, ForeignKey('repositories.id'), nullable=False)
    tag_name = Column(String(100), nullable=False)
    name = Column(String(255))
    body = Column(Text)  # Release说明
    published_at = Column(DateTime)
    download_url = Column(String(500))  # 主要下载链接
    is_prerelease = Column(Integer, default=0)  # 0: 正式版, 1: 预发布版
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关联关系
    repository = relationship("Repository", back_populates="releases")
    
    def __repr__(self):
        return f"<Release(tag='{self.tag_name}', repo='{self.repository.name if self.repository else 'Unknown'}')>"


# 创建数据库表
def create_tables():
    """创建数据库表"""
    Base.metadata.create_all(bind=engine)
    logger.info("数据库表创建完成")


# 数据库操作类
class RepositoryDAO:
    """仓库数据访问对象"""
    
    @staticmethod
    def get_session():
        return SessionLocal()
    
    @classmethod
    def add_repository(cls, repo_name: str):
        """添加仓库到监控列表"""
        session = cls.get_session()
        try:
            # 检查仓库是否已存在
            existing = session.query(Repository).filter(Repository.name == repo_name).first()
            if existing:
                return False, "仓库已存在"
            
            # 从GitHub获取仓库信息
            try:
                repo = github_client.get_repo(repo_name)
                new_repo = Repository(
                    name=repo_name,
                    full_name=repo.full_name,
                    description=repo.description,
                    stars=repo.stargazers_count,
                    forks=repo.forks_count,
                    issues=repo.open_issues_count,
                    language=repo.language
                )
                session.add(new_repo)
                session.commit()
                logger.info(f"成功添加仓库: {repo_name}")
                return True, "仓库添加成功"
            except Exception as e:
                logger.error(f"获取GitHub仓库信息失败: {e}")
                return False, f"获取仓库信息失败: {str(e)}"
        except Exception as e:
            session.rollback()
            logger.error(f"添加仓库失败: {e}")
            return False, f"添加仓库失败: {str(e)}"
        finally:
            session.close()
    
    @classmethod
    def get_all_repositories(cls):
        """获取所有监控的仓库"""
        session = cls.get_session()
        try:
            repos = session.query(Repository).order_by(Repository.stars.desc()).all()
            return repos
        finally:
            session.close()
    
    @classmethod
    def delete_repository(cls, repo_id: int):
        """删除仓库"""
        session = cls.get_session()
        try:
            repo = session.query(Repository).filter(Repository.id == repo_id).first()
            if repo:
                session.delete(repo)
                session.commit()
                logger.info(f"成功删除仓库: {repo.name}")
                return True, "仓库删除成功"
            return False, "仓库不存在"
        except Exception as e:
            session.rollback()
            logger.error(f"删除仓库失败: {e}")
            return False, f"删除仓库失败: {str(e)}"
        finally:
            session.close()
    
    @classmethod
    def get_repository_releases(cls, repo_id: int, limit: int = 20):
        """获取仓库的发布记录"""
        session = cls.get_session()
        try:
            releases = session.query(Release).filter(
                Release.repo_id == repo_id
            ).order_by(Release.published_at.desc()).limit(limit).all()
            return releases
        finally:
            session.close()


# GitHub数据同步服务
class GitHubSyncService:
    """GitHub数据同步服务"""
    
    @staticmethod
    def sync_repository_data(repo_id: int = None):
        """同步仓库数据"""
        session = RepositoryDAO.get_session()
        try:
            if repo_id:
                repos = session.query(Repository).filter(Repository.id == repo_id).all()
            else:
                repos = session.query(Repository).all()
            
            for repo in repos:
                try:
                    # 更新仓库基本信息
                    github_repo = github_client.get_repo(repo.name)
                    repo.stars = github_repo.stargazers_count
                    repo.forks = github_repo.forks_count
                    repo.issues = github_repo.open_issues_count
                    repo.updated_at = datetime.utcnow()
                    
                    # 同步Release信息
                    GitHubSyncService.sync_releases(repo, github_repo, session)
                    
                    logger.info(f"同步仓库数据完成: {repo.name}")
                except Exception as e:
                    logger.error(f"同步仓库 {repo.name} 失败: {e}")
                    continue
            
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"同步数据失败: {e}")
        finally:
            session.close()
    
    @staticmethod
    def sync_releases(repo: Repository, github_repo, session):
        """同步Release数据"""
        try:
            releases = github_repo.get_releases()
            
            # 获取最新的20个release
            for i, release in enumerate(releases):
                if i >= 20:  # 限制最多20条记录
                    break
                
                # 检查release是否已存在
                existing = session.query(Release).filter(
                    Release.repo_id == repo.id,
                    Release.tag_name == release.tag_name
                ).first()
                
                if not existing:
                    # 获取下载链接
                    download_url = ""
                    if release.assets:
                        download_url = release.assets[0].browser_download_url
                    elif release.tarball_url:
                        download_url = release.tarball_url
                    
                    new_release = Release(
                        repo_id=repo.id,
                        tag_name=release.tag_name,
                        name=release.title or release.tag_name,
                        body=release.body,
                        published_at=release.published_at,
                        download_url=download_url,
                        is_prerelease=1 if release.prerelease else 0
                    )
                    session.add(new_release)
        except Exception as e:
            logger.error(f"同步Release失败: {e}")


# Flask路由
@app.route('/')
def index():
    """首页 - 显示所有监控的仓库"""
    repositories = RepositoryDAO.get_all_repositories()
    return render_template('index.html', repositories=repositories)


@app.route('/add_repo', methods=['GET', 'POST'])
def add_repo():
    """添加仓库"""
    if request.method == 'POST':
        repo_name = request.form.get('repo_name', '').strip()
        if not repo_name:
            flash('请输入仓库名称', 'error')
            return redirect(url_for('add_repo'))

        # 验证仓库名称格式 (owner/repo)
        if '/' not in repo_name or len(repo_name.split('/')) != 2:
            flash('仓库名称格式错误，应为: owner/repo', 'error')
            return redirect(url_for('add_repo'))

        success, message = RepositoryDAO.add_repository(repo_name)
        if success:
            flash(message, 'success')
            # 立即同步新添加的仓库数据
            repos = RepositoryDAO.get_all_repositories()
            for repo in repos:
                if repo.name == repo_name:
                    GitHubSyncService.sync_repository_data(repo.id)
                    break
            return redirect(url_for('index'))
        else:
            flash(message, 'error')
            return redirect(url_for('add_repo'))

    return render_template('add_repo.html')


@app.route('/repo/<int:repo_id>')
def repo_detail(repo_id):
    """仓库详情页 - 显示Release历史"""
    session = RepositoryDAO.get_session()
    try:
        repo = session.query(Repository).filter(Repository.id == repo_id).first()
        if not repo:
            flash('仓库不存在', 'error')
            return redirect(url_for('index'))

        releases = RepositoryDAO.get_repository_releases(repo_id)
        return render_template('repo_detail.html', repository=repo, releases=releases)
    finally:
        session.close()


@app.route('/delete_repo/<int:repo_id>', methods=['POST'])
def delete_repo(repo_id):
    """删除仓库"""
    success, message = RepositoryDAO.delete_repository(repo_id)
    flash(message, 'success' if success else 'error')
    return redirect(url_for('index'))


@app.route('/sync_repo/<int:repo_id>', methods=['POST'])
def sync_repo(repo_id):
    """手动同步单个仓库数据"""
    try:
        GitHubSyncService.sync_repository_data(repo_id)
        flash('仓库数据同步成功', 'success')
    except Exception as e:
        flash(f'同步失败: {str(e)}', 'error')
    return redirect(url_for('index'))


@app.route('/sync_all', methods=['POST'])
def sync_all():
    """同步所有仓库数据"""
    try:
        GitHubSyncService.sync_repository_data()
        flash('所有仓库数据同步成功', 'success')
    except Exception as e:
        flash(f'同步失败: {str(e)}', 'error')
    return redirect(url_for('index'))


@app.route('/api/repos')
def api_repos():
    """API: 获取所有仓库数据"""
    repositories = RepositoryDAO.get_all_repositories()
    return jsonify([{
        'id': repo.id,
        'name': repo.name,
        'full_name': repo.full_name,
        'description': repo.description,
        'stars': repo.stars,
        'forks': repo.forks,
        'issues': repo.issues,
        'language': repo.language,
        'updated_at': repo.updated_at.isoformat() if repo.updated_at else None
    } for repo in repositories])


@app.route('/api/repo/<int:repo_id>/releases')
def api_repo_releases(repo_id):
    """API: 获取仓库的Release数据"""
    releases = RepositoryDAO.get_repository_releases(repo_id)
    return jsonify([{
        'id': release.id,
        'tag_name': release.tag_name,
        'name': release.name,
        'body': release.body,
        'published_at': release.published_at.isoformat() if release.published_at else None,
        'download_url': release.download_url,
        'is_prerelease': bool(release.is_prerelease)
    } for release in releases])


# 定时任务调度器
def init_scheduler():
    """初始化定时任务调度器"""
    scheduler = BackgroundScheduler()

    # 每小时同步一次所有仓库数据
    scheduler.add_job(
        func=GitHubSyncService.sync_repository_data,
        trigger="interval",
        hours=1,
        id='sync_all_repos'
    )

    scheduler.start()
    logger.info("定时任务调度器启动成功")


if __name__ == '__main__':
    # 创建数据库表
    create_tables()

    # 初始化定时任务
    init_scheduler()

    # 启动应用
    app.run(debug=True, host='0.0.0.0', port=5000)
