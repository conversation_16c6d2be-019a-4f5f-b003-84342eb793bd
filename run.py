#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GitHub Release监控系统启动脚本
"""

import os
import sys
from app import app, create_tables, init_scheduler

def main():
    """主函数"""
    print("🚀 启动GitHub Release监控系统...")
    
    # 创建数据库表
    print("📊 初始化数据库...")
    create_tables()
    
    # 初始化定时任务
    print("⏰ 启动定时任务调度器...")
    init_scheduler()
    
    # 获取配置
    host = os.getenv('APP_HOST', '0.0.0.0')
    port = int(os.getenv('APP_PORT', 5000))
    debug = os.getenv('FLASK_DEBUG', 'True').lower() == 'true'
    
    print(f"🌐 应用将在 http://{host}:{port} 启动")
    print("📝 使用说明:")
    print("   1. 访问首页查看监控的仓库列表")
    print("   2. 点击'添加仓库'添加新的GitHub仓库")
    print("   3. 点击仓库卡片查看Release历史")
    print("   4. 系统会每小时自动同步数据")
    print()
    print("⚠️  提示: 建议配置GitHub Token以获得更高的API调用限制")
    print("   在.env文件中设置GITHUB_TOKEN变量")
    print()
    
    try:
        # 启动Flask应用
        app.run(
            host=host,
            port=port,
            debug=debug,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
