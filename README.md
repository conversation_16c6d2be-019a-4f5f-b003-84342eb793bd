# GitHub Release 监控系统

一个轻量级的 GitHub 仓库 Release 监控平台，实时追踪开源项目的发布动态。

## ✨ 功能特性

- 🔍 **仓库监控**: 添加任意 GitHub 公开仓库进行监控
- 📊 **实时数据**: 自动获取 Stars、Forks、Issues 等统计数据
- 🏷️ **Release 追踪**: 实时追踪最新的 Release 发布记录
- 📱 **响应式设计**: 适配桌面和移动设备
- ⚡ **轻量级**: 基于 SQLite 数据库，单文件部署
- 🔄 **自动同步**: 定时自动更新仓库数据

## 🚀 快速开始

### 环境要求

- Python 3.13+
- uv (推荐) 或 pip

### 安装步骤

1. **克隆项目**

```bash
git clone <repository-url>
cd github-release-monitor
```

2. **安装依赖**

```bash
# 使用uv (推荐)
uv sync

# 或使用pip
pip install -r requirements.txt
```

3. **配置环境变量**

```bash
cp .env.example .env
# 编辑 .env 文件，填入GitHub Token等配置
```

4. **运行应用**

```bash
python app.py
```

5. **访问应用**
   打开浏览器访问: http://localhost:5000

## 🔧 配置说明

### GitHub Token 配置

为了获得更高的 API 调用限制，建议配置 GitHub Personal Access Token:

1. 访问 [GitHub Settings > Tokens](https://github.com/settings/tokens)
2. 点击 "Generate new token"
3. 选择权限: `public_repo` (访问公开仓库)
4. 复制生成的 token 到 `.env` 文件中的 `GITHUB_TOKEN`

### 环境变量说明

| 变量名         | 说明             | 默认值                        |
| -------------- | ---------------- | ----------------------------- |
| `SECRET_KEY`   | Flask 应用密钥   | -                             |
| `DATABASE_URL` | 数据库连接 URL   | `sqlite:///github_monitor.db` |
| `GITHUB_TOKEN` | GitHub API Token | -                             |
| `APP_HOST`     | 应用监听地址     | `0.0.0.0`                     |
| `APP_PORT`     | 应用监听端口     | `5000`                        |

## 📖 使用指南

### 添加仓库

1. 点击导航栏的"添加仓库"按钮
2. 输入仓库名称，格式为: `用户名/仓库名`
3. 例如: `microsoft/vscode`, `facebook/react`
4. 点击"添加仓库"完成添加

### 查看 Release 历史

1. 在首页点击任意仓库卡片
2. 进入仓库详情页查看 Release 历史
3. 支持查看 Release 说明、下载链接等信息

### 数据同步

- **自动同步**: 系统每小时自动同步一次所有仓库数据
- **手动同步**: 点击"同步数据"按钮立即更新
- **批量同步**: 点击导航栏"同步所有"按钮

## 🏗️ 项目结构

```
github-release-monitor/
├── app.py                 # 主应用文件
├── templates/             # HTML模板
│   ├── base.html         # 基础模板
│   ├── index.html        # 首页模板
│   ├── add_repo.html     # 添加仓库页面
│   └── repo_detail.html  # 仓库详情页面
├── static/               # 静态资源
│   ├── css/
│   │   └── style.css     # 自定义样式
│   └── js/
│       └── main.js       # JavaScript功能
├── .env.example          # 环境配置示例
├── pyproject.toml        # 项目配置
└── README.md            # 项目文档
```

## 🛠️ 技术栈

- **后端**: Flask + SQLAlchemy + APScheduler
- **前端**: Bootstrap 5 + Vanilla JavaScript
- **数据库**: SQLite
- **API**: GitHub REST API + PyGithub
- **部署**: 支持 Docker、Gunicorn 等

## 📊 数据库设计

### repositories 表

- `id`: 主键
- `name`: 仓库名称 (owner/repo)
- `full_name`: 完整名称
- `description`: 仓库描述
- `stars`: Star 数量
- `forks`: Fork 数量
- `issues`: Issue 数量
- `language`: 主要编程语言
- `created_at`: 创建时间
- `updated_at`: 更新时间

### releases 表

- `id`: 主键
- `repo_id`: 关联仓库 ID
- `tag_name`: 版本标签
- `name`: Release 名称
- `body`: Release 说明
- `published_at`: 发布时间
- `download_url`: 下载链接
- `is_prerelease`: 是否为预发布版本
- `created_at`: 创建时间

## 🚀 部署指南

### Docker 部署

```bash
# 构建镜像
docker build -t github-monitor .

# 运行容器
docker run -d -p 5000:5000 \
  -e GITHUB_TOKEN=your-token \
  -v $(pwd)/data:/app/data \
  github-monitor
```

### 生产环境部署

```bash
# 使用Gunicorn
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app

# 使用Nginx反向代理
# 配置Nginx将请求转发到Gunicorn
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📝 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- [GitHub API](https://docs.github.com/en/rest) - 提供数据支持
- [Bootstrap](https://getbootstrap.com/) - UI 框架
- [Font Awesome](https://fontawesome.com/) - 图标库
- [Flask](https://flask.palletsprojects.com/) - Web 框架
