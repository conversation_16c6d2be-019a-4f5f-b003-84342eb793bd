{% extends "base.html" %}

{% block title %}首页 - GitHub Release监控系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">
                <i class="fas fa-repository me-2"></i>
                监控仓库列表
                <span class="badge bg-primary ms-2">{{ repositories|length }}</span>
            </h1>
            
            <div>
                <a href="{{ url_for('add_repo') }}" class="btn btn-success">
                    <i class="fas fa-plus me-1"></i>添加仓库
                </a>
            </div>
        </div>
    </div>
</div>

{% if repositories %}
<div class="row">
    {% for repo in repositories %}
    <div class="col-lg-6 col-xl-4 mb-4">
        <div class="card h-100 shadow-sm">
            <div class="card-header bg-light">
                <div class="d-flex justify-content-between align-items-start">
                    <h5 class="card-title mb-0">
                        <a href="{{ url_for('repo_detail', repo_id=repo.id) }}" 
                           class="text-decoration-none">
                            {{ repo.name }}
                        </a>
                    </h5>
                    
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cog"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                                <form method="POST" action="{{ url_for('sync_repo', repo_id=repo.id) }}" class="d-inline">
                                    <button type="submit" class="dropdown-item">
                                        <i class="fas fa-sync-alt me-1"></i>同步数据
                                    </button>
                                </form>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="POST" action="{{ url_for('delete_repo', repo_id=repo.id) }}" 
                                      class="d-inline" onsubmit="return confirm('确定要删除这个仓库吗？')">
                                    <button type="submit" class="dropdown-item text-danger">
                                        <i class="fas fa-trash me-1"></i>删除
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="card-body">
                {% if repo.description %}
                <p class="card-text text-muted small mb-3">{{ repo.description[:100] }}{% if repo.description|length > 100 %}...{% endif %}</p>
                {% endif %}
                
                <div class="row text-center">
                    <div class="col-4">
                        <div class="stat-item">
                            <i class="fas fa-star text-warning"></i>
                            <div class="stat-number">{{ repo.stars }}</div>
                            <div class="stat-label">Stars</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="stat-item">
                            <i class="fas fa-code-branch text-info"></i>
                            <div class="stat-number">{{ repo.forks }}</div>
                            <div class="stat-label">Forks</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="stat-item">
                            <i class="fas fa-exclamation-circle text-danger"></i>
                            <div class="stat-number">{{ repo.issues }}</div>
                            <div class="stat-label">Issues</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card-footer bg-light">
                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-muted">
                        {% if repo.language %}
                            <i class="fas fa-code me-1"></i>{{ repo.language }}
                        {% endif %}
                    </small>
                    <small class="text-muted">
                        {% if repo.updated_at %}
                            更新: {{ repo.updated_at.strftime('%m-%d %H:%M') }}
                        {% endif %}
                    </small>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% else %}
<div class="row">
    <div class="col-12">
        <div class="text-center py-5">
            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">暂无监控仓库</h4>
            <p class="text-muted">点击上方"添加仓库"按钮开始监控GitHub仓库的Release动态</p>
            <a href="{{ url_for('add_repo') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>添加第一个仓库
            </a>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_scripts %}
<script>
// 自动刷新页面数据
function autoRefresh() {
    // 每5分钟自动刷新一次
    setTimeout(function() {
        location.reload();
    }, 300000);
}

// 页面加载完成后启动自动刷新
document.addEventListener('DOMContentLoaded', function() {
    autoRefresh();
});
</script>
{% endblock %}
