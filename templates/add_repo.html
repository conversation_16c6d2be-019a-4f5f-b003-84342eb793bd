{% extends "base.html" %}

{% block title %}添加仓库 - GitHub Release监控系统{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4 class="card-title mb-0">
                    <i class="fas fa-plus me-2"></i>添加GitHub仓库
                </h4>
            </div>
            
            <div class="card-body">
                <form method="POST" action="{{ url_for('add_repo') }}">
                    <div class="mb-3">
                        <label for="repo_name" class="form-label">
                            <i class="fab fa-github me-1"></i>仓库名称
                        </label>
                        <input type="text" 
                               class="form-control" 
                               id="repo_name" 
                               name="repo_name" 
                               placeholder="例如: microsoft/vscode" 
                               required>
                        <div class="form-text">
                            请输入完整的仓库名称，格式为: <code>用户名/仓库名</code>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>添加仓库
                        </button>
                        <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>返回首页
                        </a>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 使用说明 -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>使用说明
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        支持监控任何公开的GitHub仓库
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        自动获取仓库的Stars、Forks、Issues数量
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        实时追踪最新的Release发布记录
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        每小时自动同步一次数据
                    </li>
                </ul>
                
                <div class="alert alert-info mt-3">
                    <i class="fas fa-lightbulb me-2"></i>
                    <strong>推荐仓库示例：</strong>
                    <ul class="mb-0 mt-2">
                        <li><code>microsoft/vscode</code> - Visual Studio Code</li>
                        <li><code>facebook/react</code> - React JavaScript库</li>
                        <li><code>vuejs/vue</code> - Vue.js框架</li>
                        <li><code>golang/go</code> - Go编程语言</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const repoInput = document.getElementById('repo_name');
    
    // 输入验证
    repoInput.addEventListener('input', function() {
        const value = this.value.trim();
        const isValid = value.includes('/') && value.split('/').length === 2;
        
        if (value && !isValid) {
            this.classList.add('is-invalid');
        } else {
            this.classList.remove('is-invalid');
        }
    });
    
    // 表单提交验证
    document.querySelector('form').addEventListener('submit', function(e) {
        const repoName = repoInput.value.trim();
        if (!repoName.includes('/') || repoName.split('/').length !== 2) {
            e.preventDefault();
            alert('请输入正确的仓库名称格式: 用户名/仓库名');
            repoInput.focus();
        }
    });
});
</script>
{% endblock %}
