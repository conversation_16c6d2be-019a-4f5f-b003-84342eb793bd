{% extends "base.html" %}

{% block title %}{{ repository.name }} - GitHub Release监控系统{% endblock %}

{% block content %}
<!-- 仓库信息头部 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h1 class="h3 mb-2">
                            <i class="fab fa-github me-2"></i>
                            {{ repository.name }}
                        </h1>
                        {% if repository.description %}
                        <p class="text-muted mb-3">{{ repository.description }}</p>
                        {% endif %}
                        
                        <div class="row">
                            <div class="col-auto">
                                <span class="badge bg-warning text-dark">
                                    <i class="fas fa-star me-1"></i>{{ repository.stars }} Stars
                                </span>
                            </div>
                            <div class="col-auto">
                                <span class="badge bg-info">
                                    <i class="fas fa-code-branch me-1"></i>{{ repository.forks }} Forks
                                </span>
                            </div>
                            <div class="col-auto">
                                <span class="badge bg-danger">
                                    <i class="fas fa-exclamation-circle me-1"></i>{{ repository.issues }} Issues
                                </span>
                            </div>
                            {% if repository.language %}
                            <div class="col-auto">
                                <span class="badge bg-secondary">
                                    <i class="fas fa-code me-1"></i>{{ repository.language }}
                                </span>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="text-end">
                        <form method="POST" action="{{ url_for('sync_repo', repo_id=repository.id) }}" class="d-inline">
                            <button type="submit" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-sync-alt me-1"></i>同步数据
                            </button>
                        </form>
                        <a href="{{ url_for('index') }}" class="btn btn-outline-secondary btn-sm ms-2">
                            <i class="fas fa-arrow-left me-1"></i>返回首页
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Release列表 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">
                    <i class="fas fa-tags me-2"></i>
                    Release历史记录
                    {% if releases %}
                    <span class="badge bg-primary ms-2">{{ releases|length }}</span>
                    {% endif %}
                </h4>
            </div>
            
            <div class="card-body">
                {% if releases %}
                <div class="timeline">
                    {% for release in releases %}
                    <div class="timeline-item">
                        <div class="timeline-marker">
                            {% if release.is_prerelease %}
                            <i class="fas fa-flask text-warning"></i>
                            {% else %}
                            <i class="fas fa-tag text-success"></i>
                            {% endif %}
                        </div>
                        
                        <div class="timeline-content">
                            <div class="card">
                                <div class="card-header">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0">
                                            <span class="badge bg-dark me-2">{{ release.tag_name }}</span>
                                            {{ release.name or release.tag_name }}
                                            {% if release.is_prerelease %}
                                            <span class="badge bg-warning text-dark ms-2">Pre-release</span>
                                            {% endif %}
                                        </h5>
                                        
                                        <small class="text-muted">
                                            {% if release.published_at %}
                                            <i class="fas fa-calendar me-1"></i>
                                            {{ release.published_at.strftime('%Y-%m-%d %H:%M') }}
                                            {% endif %}
                                        </small>
                                    </div>
                                </div>
                                
                                {% if release.body %}
                                <div class="card-body">
                                    <div class="release-notes">
                                        {{ release.body|replace('\n', '<br>')|safe }}
                                    </div>
                                </div>
                                {% endif %}
                                
                                {% if release.download_url %}
                                <div class="card-footer">
                                    <a href="{{ release.download_url }}" 
                                       target="_blank" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-download me-1"></i>下载
                                    </a>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">暂无Release记录</h5>
                    <p class="text-muted">该仓库还没有发布任何Release版本</p>
                    <form method="POST" action="{{ url_for('sync_repo', repo_id=repository.id) }}" class="d-inline">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-sync-alt me-1"></i>立即同步
                        </button>
                    </form>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_head %}
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 10px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: white;
    border: 2px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.timeline-content {
    margin-left: 20px;
}

.release-notes {
    max-height: 300px;
    overflow-y: auto;
    font-size: 14px;
    line-height: 1.6;
}

.release-notes pre {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    font-size: 12px;
}
</style>
{% endblock %}
